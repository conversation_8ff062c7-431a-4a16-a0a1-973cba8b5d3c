//SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import {FixedPointMathLib} from "../contracts/libraries/FixedPointMathLib.sol";
import {OrderBookErrors, KuruAMMVaultErrors, MarginAccountErrors} from "../contracts/libraries/Errors.sol";
import {IOrderBook} from "../contracts/interfaces/IOrderBook.sol";
import {KuruAMMVault} from "../contracts/KuruAMMVault.sol";
import {OrderBook} from "../contracts/OrderBook.sol";
import {MarginAccount} from "../contracts/MarginAccount.sol";
import {Router} from "../contracts/Router.sol";
import {ERC20} from "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import {ERC1967Proxy} from "@openzeppelin/contracts/proxy/ERC1967/ERC1967Proxy.sol";
import {Create2} from "@openzeppelin/contracts/utils/Create2.sol";
import {MintableERC20} from "./lib/MintableERC20.sol";
import {PropertiesAsserts} from "./Helper.sol";

/**
 * @title NullifyWithdrawalBugPOC
 * @dev POC to verify the alleged bug in KuruAMMVault nullify withdrawal calculations
 * 
 * Issue: When withdrawing shares such that new ask/bid sizes < current partially filled sizes,
 * the code incorrectly calculates _baseOwedToVault and _quoteOwedToVault with unwarranted negative terms:
 * - _baseOwedToVault = (partial_ask - partial_bid) * (10**base_dec / size_prec)
 * - _quoteOwedToVault = (partial_bid * bid_price - partial_ask * ask_price) * (10**quote_dec / vault_prec)
 * 
 * The negative terms (- partial_bid in base, - partial_ask * ask in quote) create phantom adjustments
 * that lead to underpayment of withdrawing users.
 */
contract NullifyWithdrawalBugPOC is Test, PropertiesAsserts {
    uint96 constant SIZE_PRECISION = 10 ** 10;
    uint32 constant PRICE_PRECISION = 10 ** 2;
    uint32 _tickSize;
    uint96 _minSize;
    uint96 _maxSize;
    uint96 _takerFeeBps;
    uint256 _makerFeeBps;
    uint32 _maxPrice;
    uint256 vaultPricePrecision;
    OrderBook orderBook;
    KuruAMMVault vault;
    Router router;
    MarginAccount marginAccount;

    MintableERC20 eth;
    MintableERC20 usdc;
    uint256 SEED = 2;
    address lastGenAddress;
    address trustedForwarder;

    function setUp() public {
        eth = new MintableERC20("ETH", "ETH");
        usdc = new MintableERC20("USDC", "USDC");
        uint96 _sizePrecision = 10 ** 10;
        uint32 _pricePrecision = 10 ** 2;
        vaultPricePrecision = 10 ** 18;
        _tickSize = _pricePrecision / 2;
        _minSize = 2 * 10 ** 8;
        _maxSize = 10 ** 12;
        _maxPrice = type(uint32).max / 200;
        _takerFeeBps = 0;
        _makerFeeBps = 0;
        OrderBook.OrderBookType _type;
        OrderBook implementation = new OrderBook();
        Router routerImplementation = new Router();
        address routerProxy = Create2.deploy(
            0,
            bytes32(keccak256("")),
            abi.encodePacked(type(ERC1967Proxy).creationCode, abi.encode(routerImplementation, bytes("")))
        );
        router = Router(payable(routerProxy));
        trustedForwarder = address(0x123);
        marginAccount = new MarginAccount();
        marginAccount = MarginAccount(payable(address(new ERC1967Proxy(address(marginAccount), ""))));
        marginAccount.initialize(address(this), address(router), address(router), trustedForwarder);
        uint96 SPREAD = 100;
        KuruAMMVault kuruAmmVaultImplementation = new KuruAMMVault();
        router.initialize(address(this), address(marginAccount), address(implementation), address(kuruAmmVaultImplementation), trustedForwarder);

        address proxy = router.deployProxy(
            _type,
            address(eth),
            address(usdc),
            _sizePrecision,
            _pricePrecision,
            _tickSize,
            _minSize,
            _maxSize,
            _takerFeeBps,
            _makerFeeBps,
            SPREAD
        );
        orderBook = OrderBook(proxy);
        (address _kuruVault,,,,,,,) = orderBook.getVaultParams();
        vault = KuruAMMVault(payable(_kuruVault));
    }

    function genAddress() internal returns (address) {
        uint256 _seed = SEED;
        uint256 privateKeyGen = uint256(keccak256(abi.encodePacked(bytes32(_seed))));
        address derived = vm.addr(privateKeyGen);
        ++SEED;
        lastGenAddress = derived;
        return derived;
    }

    function _adjustPriceAndSize(uint32 _price, uint96 _size) internal returns (uint32, uint96) {
        uint32 _newPrice = uint32(clampBetween(_price, _tickSize, _maxPrice));
        uint96 _newSize = uint96(clampBetween(_size, _minSize + 1, _maxSize - 1));
        _newPrice = _newPrice - _newPrice % _tickSize;
        return (_newPrice, _newSize);
    }

    function _adjustPriceAndSizeForVault(uint256 _price, uint96 _size) internal returns (uint256, uint96) {
        uint256 _newPrice = clampBetween(_price, 10000, 100000);
        uint96 _newSize = uint96(clampBetween(_size, _minSize * 100, _maxSize / 100));
        return (_newPrice, _newSize);
    }

    /**
     * @dev Test Case 1: Verify underpayment occurs during nullify withdrawal with only partial ask fills
     * This test demonstrates the bug where phantom negative terms cause underpayment
     */
    function test_NullifyWithdrawalUnderpaymentWithPartialAsk() public {
        // Setup: Create a vault with significant liquidity
        uint256 targetVaultPrice = 50000; // $50,000 per ETH
        uint96 targetVaultSize = 10 ** 11; // Large size for clear demonstration
        
        (targetVaultPrice, targetVaultSize) = _adjustPriceAndSizeForVault(targetVaultPrice, targetVaultSize);
        
        uint256 amountBase = (targetVaultSize * 10 ** eth.decimals()) / SIZE_PRECISION;
        uint256 amountQuote = (targetVaultSize * 10 ** usdc.decimals() * targetVaultPrice) / (vaultPricePrecision * SIZE_PRECISION);
        
        address vaultMaker = genAddress();
        eth.mint(vaultMaker, amountBase);
        usdc.mint(vaultMaker, amountQuote);
        
        vm.startPrank(vaultMaker);
        eth.approve(address(vault), amountBase);
        usdc.approve(address(vault), amountQuote);
        uint256 shares = vault.deposit(amountBase, amountQuote, vaultMaker);
        vm.stopPrank();
        
        // Get initial vault state
        (uint256 initialBaseReserve, uint256 initialQuoteReserve) = vault.totalAssets();
        (,,,, uint96 vaultAskOrderSize, uint96 vaultBidOrderSize,,) = orderBook.getVaultParams();
        
        // Create partial ask fill (someone buys from vault)
        uint96 partialAskSize = vaultAskOrderSize / 3; // Fill 1/3 of ask order
        uint256 askTakerQuoteAmount = (partialAskSize * targetVaultPrice * 10 ** usdc.decimals()) / (SIZE_PRECISION * vaultPricePrecision);
        
        address partialAskTaker = genAddress();
        usdc.mint(partialAskTaker, askTakerQuoteAmount);
        
        vm.startPrank(partialAskTaker);
        usdc.approve(address(orderBook), askTakerQuoteAmount);
        orderBook.placeAndExecuteMarketBuy(partialAskSize, type(uint256).max, false, true);
        vm.stopPrank();
        
        // Verify partial fill occurred
        (,, uint96 partiallyFilledBid,, uint96 partiallyFilledAsk,,,) = orderBook.getVaultParams();
        assertGt(partiallyFilledAsk, 0, "Partial ask fill should have occurred");
        assertEq(partiallyFilledBid, 0, "No bid fill should have occurred");
        
        // Calculate expected fair withdrawal (proportional share of total assets including unlocked)
        uint256 expectedBaseFromUnlock = (partiallyFilledAsk * 10 ** eth.decimals()) / SIZE_PRECISION;
        uint256 expectedTotalBase = initialBaseReserve + expectedBaseFromUnlock;
        uint256 expectedFairBaseWithdrawal = (shares * expectedTotalBase) / vault.totalSupply();
        uint256 expectedFairQuoteWithdrawal = (shares * initialQuoteReserve) / vault.totalSupply();
        
        // Perform withdrawal that triggers nullify (withdraw all shares)
        vm.startPrank(vaultMaker);
        (uint256 actualBaseWithdrawal, uint256 actualQuoteWithdrawal) = vault.withdraw(shares, vaultMaker, vaultMaker);
        vm.stopPrank();
        
        // BUG VERIFICATION: User should receive fair proportional share but gets less due to phantom negative terms
        // The bug causes underpayment because:
        // _baseOwedToVault = (partial_ask - 0) = partial_ask (correct)
        // But then user gets: proportional_share - partial_ask instead of proportional_share
        // This is wrong because the unlocked base (partial_ask) should be ADDED to reserves, not subtracted from user payout
        
        assertLt(actualBaseWithdrawal, expectedFairBaseWithdrawal, "BUG CONFIRMED: User receives less base than fair share");
        
        // Calculate the underpayment amount
        uint256 baseUnderpayment = expectedFairBaseWithdrawal - actualBaseWithdrawal;
        uint256 underpaymentPercentage = (baseUnderpayment * 10000) / expectedFairBaseWithdrawal; // in basis points
        
        // Log the underpayment for analysis
        emit LogUint256("Expected fair base withdrawal", expectedFairBaseWithdrawal);
        emit LogUint256("Actual base withdrawal", actualBaseWithdrawal);
        emit LogUint256("Base underpayment", baseUnderpayment);
        emit LogUint256("Underpayment percentage (bps)", underpaymentPercentage);
        
        // Verify significant underpayment occurred (should be > 0 if bug exists)
        assertGt(baseUnderpayment, 0, "BUG CONFIRMED: Significant underpayment occurred");
        
        // Additional verification: Check that partial fills were nullified
        (,, uint96 finalPartiallyFilledBid,, uint96 finalPartiallyFilledAsk,,,) = orderBook.getVaultParams();
        assertEq(finalPartiallyFilledAsk, 0, "Partial ask should be nullified after withdrawal");
        assertEq(finalPartiallyFilledBid, 0, "Partial bid should remain 0");
    }

    /**
     * @dev Test Case 2: Verify underpayment occurs during nullify withdrawal with only partial bid fills
     * This test demonstrates the bug where phantom negative terms cause underpayment in quote token
     */
    function test_NullifyWithdrawalUnderpaymentWithPartialBid() public {
        // Setup: Create a vault with significant liquidity
        uint256 targetVaultPrice = 50000; // $50,000 per ETH
        uint96 targetVaultSize = 10 ** 11; // Large size for clear demonstration

        (targetVaultPrice, targetVaultSize) = _adjustPriceAndSizeForVault(targetVaultPrice, targetVaultSize);

        uint256 amountBase = (targetVaultSize * 10 ** eth.decimals()) / SIZE_PRECISION;
        uint256 amountQuote = (targetVaultSize * 10 ** usdc.decimals() * targetVaultPrice) / (vaultPricePrecision * SIZE_PRECISION);

        address vaultMaker = genAddress();
        eth.mint(vaultMaker, amountBase);
        usdc.mint(vaultMaker, amountQuote);

        vm.startPrank(vaultMaker);
        eth.approve(address(vault), amountBase);
        usdc.approve(address(vault), amountQuote);
        uint256 shares = vault.deposit(amountBase, amountQuote, vaultMaker);
        vm.stopPrank();

        // Get initial vault state
        (uint256 initialBaseReserve, uint256 initialQuoteReserve) = vault.totalAssets();
        (,,,, uint96 vaultAskOrderSize, uint96 vaultBidOrderSize,,) = orderBook.getVaultParams();

        // Create partial bid fill (someone sells to vault)
        uint96 partialBidSize = vaultBidOrderSize / 3; // Fill 1/3 of bid order
        uint256 bidTakerBaseAmount = (partialBidSize * 10 ** eth.decimals()) / SIZE_PRECISION;

        address partialBidTaker = genAddress();
        eth.mint(partialBidTaker, bidTakerBaseAmount);

        vm.startPrank(partialBidTaker);
        eth.approve(address(orderBook), bidTakerBaseAmount);
        orderBook.placeAndExecuteMarketSell(partialBidSize, 0, false, true);
        vm.stopPrank();

        // Verify partial fill occurred
        (,, uint96 partiallyFilledBid,, uint96 partiallyFilledAsk,,,) = orderBook.getVaultParams();
        assertGt(partiallyFilledBid, 0, "Partial bid fill should have occurred");
        assertEq(partiallyFilledAsk, 0, "No ask fill should have occurred");

        // Calculate expected fair withdrawal (proportional share of total assets including unlocked)
        uint256 expectedQuoteFromUnlock = (partiallyFilledBid * targetVaultPrice * 10 ** usdc.decimals()) / (SIZE_PRECISION * vaultPricePrecision);
        uint256 expectedTotalQuote = initialQuoteReserve + expectedQuoteFromUnlock;
        uint256 expectedFairBaseWithdrawal = (shares * initialBaseReserve) / vault.totalSupply();
        uint256 expectedFairQuoteWithdrawal = (shares * expectedTotalQuote) / vault.totalSupply();

        // Perform withdrawal that triggers nullify (withdraw all shares)
        vm.startPrank(vaultMaker);
        (uint256 actualBaseWithdrawal, uint256 actualQuoteWithdrawal) = vault.withdraw(shares, vaultMaker, vaultMaker);
        vm.stopPrank();

        // BUG VERIFICATION: User should receive fair proportional share but gets less due to phantom negative terms
        // The bug causes underpayment because:
        // _quoteOwedToVault = (partial_bid * bid_price - 0) = partial_bid * bid_price (positive, correct)
        // But then user gets: proportional_share - (partial_bid * bid_price) instead of proportional_share
        // This is wrong because the unlocked quote should be ADDED to reserves, not subtracted from user payout

        assertLt(actualQuoteWithdrawal, expectedFairQuoteWithdrawal, "BUG CONFIRMED: User receives less quote than fair share");

        // Calculate the underpayment amount
        uint256 quoteUnderpayment = expectedFairQuoteWithdrawal - actualQuoteWithdrawal;
        uint256 underpaymentPercentage = (quoteUnderpayment * 10000) / expectedFairQuoteWithdrawal; // in basis points

        // Log the underpayment for analysis
        emit LogUint256("Expected fair quote withdrawal", expectedFairQuoteWithdrawal);
        emit LogUint256("Actual quote withdrawal", actualQuoteWithdrawal);
        emit LogUint256("Quote underpayment", quoteUnderpayment);
        emit LogUint256("Underpayment percentage (bps)", underpaymentPercentage);

        // Verify significant underpayment occurred (should be > 0 if bug exists)
        assertGt(quoteUnderpayment, 0, "BUG CONFIRMED: Significant underpayment occurred");

        // Additional verification: Check that partial fills were nullified
        (,, uint96 finalPartiallyFilledBid,, uint96 finalPartiallyFilledAsk,,,) = orderBook.getVaultParams();
        assertEq(finalPartiallyFilledBid, 0, "Partial bid should be nullified after withdrawal");
        assertEq(finalPartiallyFilledAsk, 0, "Partial ask should remain 0");
    }

    /**
     * @dev Test Case 3: Verify severe underpayment with both partial ask and bid fills
     * This demonstrates the worst-case scenario where phantom negative terms affect both base and quote
     */
    function test_NullifyWithdrawalSevereUnderpaymentWithBothPartials() public {
        // Setup: Create a vault with significant liquidity
        uint256 targetVaultPrice = 50000; // $50,000 per ETH
        uint96 targetVaultSize = 10 ** 11; // Large size for clear demonstration

        (targetVaultPrice, targetVaultSize) = _adjustPriceAndSizeForVault(targetVaultPrice, targetVaultSize);

        uint256 amountBase = (targetVaultSize * 10 ** eth.decimals()) / SIZE_PRECISION;
        uint256 amountQuote = (targetVaultSize * 10 ** usdc.decimals() * targetVaultPrice) / (vaultPricePrecision * SIZE_PRECISION);

        address vaultMaker = genAddress();
        eth.mint(vaultMaker, amountBase);
        usdc.mint(vaultMaker, amountQuote);

        vm.startPrank(vaultMaker);
        eth.approve(address(vault), amountBase);
        usdc.approve(address(vault), amountQuote);
        uint256 shares = vault.deposit(amountBase, amountQuote, vaultMaker);
        vm.stopPrank();

        // Get initial vault state
        (uint256 initialBaseReserve, uint256 initialQuoteReserve) = vault.totalAssets();
        (,,,, uint96 vaultAskOrderSize, uint96 vaultBidOrderSize,,) = orderBook.getVaultParams();

        // Create partial ask fill (someone buys from vault)
        uint96 partialAskSize = vaultAskOrderSize / 4; // Fill 1/4 of ask order
        uint256 askTakerQuoteAmount = (partialAskSize * targetVaultPrice * 10 ** usdc.decimals()) / (SIZE_PRECISION * vaultPricePrecision);

        address partialAskTaker = genAddress();
        usdc.mint(partialAskTaker, askTakerQuoteAmount);

        vm.startPrank(partialAskTaker);
        usdc.approve(address(orderBook), askTakerQuoteAmount);
        orderBook.placeAndExecuteMarketBuy(partialAskSize, type(uint256).max, false, true);
        vm.stopPrank();

        // Create partial bid fill (someone sells to vault)
        uint96 partialBidSize = vaultBidOrderSize / 4; // Fill 1/4 of bid order
        uint256 bidTakerBaseAmount = (partialBidSize * 10 ** eth.decimals()) / SIZE_PRECISION;

        address partialBidTaker = genAddress();
        eth.mint(partialBidTaker, bidTakerBaseAmount);

        vm.startPrank(partialBidTaker);
        eth.approve(address(orderBook), bidTakerBaseAmount);
        orderBook.placeAndExecuteMarketSell(partialBidSize, 0, false, true);
        vm.stopPrank();

        // Verify both partial fills occurred
        (,, uint96 partiallyFilledBid,, uint96 partiallyFilledAsk,,,) = orderBook.getVaultParams();
        assertGt(partiallyFilledAsk, 0, "Partial ask fill should have occurred");
        assertGt(partiallyFilledBid, 0, "Partial bid fill should have occurred");

        // Calculate expected fair withdrawal (proportional share of total assets including unlocked)
        uint256 expectedBaseFromUnlock = (partiallyFilledAsk * 10 ** eth.decimals()) / SIZE_PRECISION;
        uint256 expectedQuoteFromUnlock = (partiallyFilledBid * targetVaultPrice * 10 ** usdc.decimals()) / (SIZE_PRECISION * vaultPricePrecision);
        uint256 expectedTotalBase = initialBaseReserve + expectedBaseFromUnlock;
        uint256 expectedTotalQuote = initialQuoteReserve + expectedQuoteFromUnlock;
        uint256 expectedFairBaseWithdrawal = (shares * expectedTotalBase) / vault.totalSupply();
        uint256 expectedFairQuoteWithdrawal = (shares * expectedTotalQuote) / vault.totalSupply();

        // Perform withdrawal that triggers nullify (withdraw all shares)
        vm.startPrank(vaultMaker);
        (uint256 actualBaseWithdrawal, uint256 actualQuoteWithdrawal) = vault.withdraw(shares, vaultMaker, vaultMaker);
        vm.stopPrank();

        // BUG VERIFICATION: The phantom negative terms create severe underpayment
        // _baseOwedToVault = (partial_ask - partial_bid) - could be negative if partial_bid > partial_ask
        // _quoteOwedToVault = (partial_bid * bid_price - partial_ask * ask_price) - could be negative
        // These phantom adjustments cause significant underpayment

        assertLt(actualBaseWithdrawal, expectedFairBaseWithdrawal, "BUG CONFIRMED: User receives less base than fair share");
        assertLt(actualQuoteWithdrawal, expectedFairQuoteWithdrawal, "BUG CONFIRMED: User receives less quote than fair share");

        // Calculate the underpayment amounts
        uint256 baseUnderpayment = expectedFairBaseWithdrawal - actualBaseWithdrawal;
        uint256 quoteUnderpayment = expectedFairQuoteWithdrawal - actualQuoteWithdrawal;
        uint256 baseUnderpaymentPercentage = (baseUnderpayment * 10000) / expectedFairBaseWithdrawal;
        uint256 quoteUnderpaymentPercentage = (quoteUnderpayment * 10000) / expectedFairQuoteWithdrawal;

        // Log the underpayments for analysis
        emit LogUint256("Expected fair base withdrawal", expectedFairBaseWithdrawal);
        emit LogUint256("Actual base withdrawal", actualBaseWithdrawal);
        emit LogUint256("Base underpayment", baseUnderpayment);
        emit LogUint256("Base underpayment percentage (bps)", baseUnderpaymentPercentage);

        emit LogUint256("Expected fair quote withdrawal", expectedFairQuoteWithdrawal);
        emit LogUint256("Actual quote withdrawal", actualQuoteWithdrawal);
        emit LogUint256("Quote underpayment", quoteUnderpayment);
        emit LogUint256("Quote underpayment percentage (bps)", quoteUnderpaymentPercentage);

        // Verify significant underpayment occurred in both tokens
        assertGt(baseUnderpayment, 0, "BUG CONFIRMED: Significant base underpayment occurred");
        assertGt(quoteUnderpayment, 0, "BUG CONFIRMED: Significant quote underpayment occurred");

        // Additional verification: Check that partial fills were nullified
        (,, uint96 finalPartiallyFilledBid,, uint96 finalPartiallyFilledAsk,,,) = orderBook.getVaultParams();
        assertEq(finalPartiallyFilledAsk, 0, "Partial ask should be nullified after withdrawal");
        assertEq(finalPartiallyFilledBid, 0, "Partial bid should be nullified after withdrawal");
    }
