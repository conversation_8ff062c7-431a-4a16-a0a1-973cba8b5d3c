in KuruAMMVault

Incorrect Unlocked Asset Calculation and Underpayment During Nullify Withdrawals

Description: When withdrawing shares such that new ask/bid sizes < current partially filled (remaining) sizes (_partiallyFilledAskSize > _newAskSize or _partiallyFilledBidSize > _newBidSize), the code sets nullify=true and calls market.updateVaultOrdSz to cancel current vault orders, unlocking locked assets back to reserves. However, the adjustment to reserves and user owed amounts is incorrect:

_baseOwedToVault = (partial_ask - partial_bid) * (10**base_dec / size_prec)
_quoteOwedToVault = (mulDivUp(partial_bid, bid_price, size_prec) - mulDiv(partial_ask, ask_price, size_prec)) * (10**quote_dec / vault_prec)
The negative terms (- partial_bid in base, - partial_ask * ask in quote) are unwarranted. Standard locking: Canceling ask unlocks base (partial_ask * unit); canceling bid unlocks quote (partial_bid * bid * unit). No justification for subtracting bid from base or ask quote from quote owed—these create "phantom" adjustments.
After adjusting reserves (reserve += owed if >0, or reserve -= |owed| if <0), user owed = proportional of adjusted reserve ± |owed| (specifically, - owed if >0, + |owed| if <0).


Discrepancy from Claims: Docs claim proportional withdrawals anytime, with no penalties. This unmentioned transition (reducing below partials) causes underpayment: User gets  (shares/supply) * (old_reserve + owed) - owed (if owed >0), which is less than fair proportional of total (including unlocked). Remaining LPs benefit unfairly. Negative terms can make owed negative, inflating/uninflating reserves incorrectly (e.g., with only partial_ask, adds phantom quote to reserve).
Impact: Fund loss for withdrawing users (high if large partials). Exploitable by timing withdrawals after partial fills to minimize own loss or force others'.